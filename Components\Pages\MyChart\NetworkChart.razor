@using ApexCharts
@using Dashboard_Yuntech.Models.ChartModels
@using Dashboard_Yuntech.Service
@inject ChartDataService _chartDataService
@inject IJSRuntime JSRuntime

<MyCard_dark2 Year="@Year" Title="@Title" IsModal="true" TableColumns="@TableColumns" TableData="@TableData" OnExportExcel="@ExportToExcel" ModalId="@ModalId" EnableChartModal="true" ChartModalContent="@ChartModalContent">
    @if (isLoading)
    {
        <div class="text-center mt-5">
            <div class="spinner-border mt-5" role="status">
            </div>
        </div>
    }
    else if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger">@errorMessage</div>
    }
    else if (ChartData == null || !ChartData.Any())
    {
        <div class="alert alert-info">沒有可顯示的數據</div>
    }
    else
    {
        <ApexChart TItem="NetworkTrafficModel" Options="ChartOptions">
            <ApexPointSeries TItem="NetworkTrafficModel"
                           Name="入站流量 (Inbound)"
                           Items="@ChartData"
                           XValue="e => e.Time"
                           YValue="e => (decimal?)e.Inbound"
                           SeriesType="SeriesType.Area" />
            <ApexPointSeries TItem="NetworkTrafficModel"
                           Name="出站流量 (Outbound)"
                           Items="@ChartData"
                           XValue="e => e.Time"
                           YValue="e => (decimal?)e.Outbound"
                           SeriesType="SeriesType.Area" />
        </ApexChart>
    }
</MyCard_dark2>

@code {
    [Parameter] public string Title { get; set; } = "網路流量統計圖表";
    [Parameter] public string? Year { get; set; }
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }
    [Parameter] public List<string>? TableColumns { get; set; }
    [Parameter] public List<NetworkTrafficModel> ChartData { get; set; } = new();
    [Parameter] public ApexChartOptions<NetworkTrafficModel> ChartOptions { get; set; } = new();
    [Parameter] public string ModalId { get; set; } = "networkTrafficChart";
    [Parameter] public string bodyCss { get; set; } = "bg-dark";
    [Parameter] public EventCallback<List<Dictionary<string, object>>> OnDataLoaded { get; set; }

    private bool isLoading = false;
    private string? errorMessage;

    // 圖表模態框內容
    private RenderFragment ChartModalContent => builder =>
    {
        builder.OpenElement(0, "div");
        builder.AddAttribute(1, "style", "height: 600px; width: 100%;");

        if (isLoading)
        {
            builder.OpenElement(2, "div");
            builder.AddAttribute(3, "class", "text-center mt-5");
            builder.OpenElement(4, "div");
            builder.AddAttribute(5, "class", "spinner-border mt-5");
            builder.AddAttribute(6, "role", "status");
            builder.CloseElement();
            builder.CloseElement();
        }
        else if (!string.IsNullOrEmpty(errorMessage))
        {
            builder.OpenElement(7, "div");
            builder.AddAttribute(8, "class", "alert alert-danger");
            builder.AddContent(9, errorMessage);
            builder.CloseElement();
        }
        else if (ChartData == null || !ChartData.Any())
        {
            builder.OpenElement(10, "div");
            builder.AddAttribute(11, "class", "alert alert-info");
            builder.AddContent(12, "沒有可顯示的數據");
            builder.CloseElement();
        }
        else
        {
            // 創建放大版本的圖表選項
            var enlargedChartOptions = CreateEnlargedChartOptions();

            builder.OpenComponent<ApexChart<NetworkTrafficModel>>(13);
            builder.AddAttribute(14, "Options", enlargedChartOptions);
            builder.AddAttribute(15, "ChildContent", (RenderFragment)(chartBuilder =>
            {
                chartBuilder.OpenComponent<ApexPointSeries<NetworkTrafficModel>>(0);
                chartBuilder.AddAttribute(1, "Name", "入站流量 (Inbound)");
                chartBuilder.AddAttribute(2, "Items", ChartData);
                chartBuilder.AddAttribute(3, "XValue", (Func<NetworkTrafficModel, object>)(e => e.Time));
                chartBuilder.AddAttribute(4, "YValue", (Func<NetworkTrafficModel, decimal?>)(e => (decimal?)e.Inbound));
                chartBuilder.AddAttribute(5, "SeriesType", SeriesType.Area);
                chartBuilder.CloseComponent();

                chartBuilder.OpenComponent<ApexPointSeries<NetworkTrafficModel>>(6);
                chartBuilder.AddAttribute(7, "Name", "出站流量 (Outbound)");
                chartBuilder.AddAttribute(8, "Items", ChartData);
                chartBuilder.AddAttribute(9, "XValue", (Func<NetworkTrafficModel, object>)(e => e.Time));
                chartBuilder.AddAttribute(10, "YValue", (Func<NetworkTrafficModel, decimal?>)(e => (decimal?)e.Outbound));
                chartBuilder.AddAttribute(11, "SeriesType", SeriesType.Area);
                chartBuilder.CloseComponent();
            }));
            builder.CloseComponent();
        }

        builder.CloseElement();
    };

    protected override async Task OnInitializedAsync()
    {
        ChartOptions.Yaxis = new List<YAxis>
        {
            new YAxis
            {
                Labels = new YAxisLabels
                {
                    Formatter = @"
                        function (value) {
                            if (value >= 1000000) {
                                return Math.round(value / 1000000) + 'M';
                            }
                            if (value >= 1000) {
                                return Math.round(value / 1000) + 'K';
                            }
                            return Math.round(value);
                        }
                    "
                }
            }
        };
        // 準備表格資料
        PrepareTableData();
        
        // 通知父元件數據已加載
        if (OnDataLoaded.HasDelegate && TableData != null)
        {
            await OnDataLoaded.InvokeAsync(TableData);
        }
    }

    protected override Task OnParametersSetAsync()
    {
        // 當參數變更時重新準備表格資料
        if (ChartData != null && ChartData.Any())
        {
            PrepareTableData();

            // 設定Y軸刻度
            var maxInbound = ChartData.Max(d => d.Inbound);
            var maxOutbound = ChartData.Max(d => d.Outbound);
            var maxVal = Math.Max(maxInbound, maxOutbound);

            if (maxVal > 0)
            {
                var yAxisMax = (Math.Ceiling(maxVal / 100000000.0) * 100000000);
                if(yAxisMax < maxVal)
                {
                    yAxisMax += 100000000;
                }

                if (ChartOptions.Yaxis.FirstOrDefault() != null)
                {
                    ChartOptions.Yaxis.First().Max = (double)yAxisMax;
                    ChartOptions.Yaxis.First().TickAmount = (int)(yAxisMax / 100000000);
                }
            }
        }
        return Task.CompletedTask;
    }

    private void PrepareTableData()
    {
        if (ChartData != null && ChartData.Any())
        {
            TableData = new List<Dictionary<string, object>>();
            TableColumns = new List<string> { "時間", "入站流量", "出站流量" };

            foreach (var item in ChartData)
            {
                var row = new Dictionary<string, object>
                {
                    ["時間"] = !string.IsNullOrEmpty(item.OriginalTimeFormat) ? item.OriginalTimeFormat : item.Time,
                    ["入站流量"] = FormatTrafficValue(item.Inbound),
                    ["出站流量"] = FormatTrafficValue(item.Outbound)
                };
                TableData.Add(row);
            }
        }
    }
    
    // 新增：格式化流量數值的方法
    private string FormatTrafficValue(int value)
    {
        if (value >= 1000000000)
            return $"{(value / 1000000000.0):F2} Gbps";
        if (value >= 1000000)
            return $"{(value / 1000000.0):F2} Mbps";
        if (value >= 1000)
            return $"{(value / 1000.0):F2} Kbps";
        return $"{value} bps";
    }

    private async Task ExportToExcel()
    {
        try
        {
            if (TableData == null || !TableData.Any())
            {
                return;
            }

            var fileName = $"{Title}_{DateTime.Now:yyyyMMddHHmm}.csv";
            var csvContent = GenerateCsvContent();
            var bytes = System.Text.Encoding.UTF8.GetBytes("\uFEFF" + csvContent);
            var base64 = Convert.ToBase64String(bytes);

            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64, "text/csv");
        }
        catch (Exception ex)
        {
            errorMessage = $"匯出失敗: {ex.Message}";
        }
    }

    private string GenerateCsvContent()
    {
        if (TableData == null || !TableData.Any() || TableColumns == null)
            return "";

        var csv = new System.Text.StringBuilder();
        
        // 新增標題
        csv.AppendLine("連接埠名稱:YUNTECH - Traffic - port17 (FTTB_HiNet_55Y0)");
        csv.AppendLine($"標題:{Title}");
        csv.AppendLine(); // Add an empty line for spacing

        // 加入標題列
        csv.AppendLine(string.Join(",", TableColumns));
        
        // 加入資料列
        foreach (var row in TableData)
        {
            var values = TableColumns.Select(col => row.ContainsKey(col) ? row[col]?.ToString()?.Replace(",", ";") ?? "" : "");
            csv.AppendLine(string.Join(",", values));
        }
        
        return csv.ToString();
    }

    // 創建放大版本的圖表選項
    private ApexChartOptions<NetworkTrafficModel> CreateEnlargedChartOptions()
    {
        // 複製原始圖表選項
        var enlargedOptions = new ApexChartOptions<NetworkTrafficModel>();

        // 複製基本設定
        enlargedOptions.Chart = new Chart
        {
            Type = ChartOptions.Chart?.Type ?? ChartType.Area,
            Height = 600, // 放大版本使用更大的高度
            Background = ChartOptions.Chart?.Background ?? "transparent",
            Toolbar = new Toolbar
            {
                Show = true, // 在放大版本中顯示工具列
                Tools = new Tools
                {
                    Download = true,
                    Selection = true,
                    Zoom = true,
                    Zoomin = true,
                    Zoomout = true,
                    Pan = true,
                    Reset = true
                }
            }
        };

        // 複製顏色設定
        enlargedOptions.Colors = ChartOptions.Colors;

        // 複製 Y 軸設定
        enlargedOptions.Yaxis = new List<YAxis>();
        if (ChartOptions.Yaxis != null && ChartOptions.Yaxis.Any())
        {
            foreach (var yAxis in ChartOptions.Yaxis)
            {
                enlargedOptions.Yaxis.Add(new YAxis
                {
                    Labels = new YAxisLabels
                    {
                        Formatter = yAxis.Labels?.Formatter ?? @"
                            function (value) {
                                if (value >= 1000000) {
                                    return Math.round(value / 1000000) + 'M';
                                }
                                if (value >= 1000) {
                                    return Math.round(value / 1000) + 'K';
                                }
                                return Math.round(value);
                            }
                        "
                    },
                    Title = yAxis.Title,
                    Max = yAxis.Max,
                    TickAmount = yAxis.TickAmount
                });
            }
        }

        // 複製 X 軸設定
        enlargedOptions.Xaxis = new XAxis
        {
            Labels = ChartOptions.Xaxis?.Labels != null ? new XAxisLabels
            {
                Style = ChartOptions.Xaxis.Labels.Style,
                ShowDuplicates = ChartOptions.Xaxis.Labels.ShowDuplicates,
                Rotate = ChartOptions.Xaxis.Labels.Rotate,
                RotateAlways = ChartOptions.Xaxis.Labels.RotateAlways,
                Formatter = ChartOptions.Xaxis.Labels.Formatter
            } : null
        };

        // 複製圖例設定
        enlargedOptions.Legend = ChartOptions.Legend != null ? new Legend
        {
            Position = ChartOptions.Legend.Position,
            FontSize = ChartOptions.Legend.FontSize
        } : null;

        // 複製其他設定
        enlargedOptions.Stroke = ChartOptions.Stroke;
        enlargedOptions.Fill = ChartOptions.Fill;
        enlargedOptions.Markers = ChartOptions.Markers;
        enlargedOptions.Grid = ChartOptions.Grid;
        enlargedOptions.Tooltip = ChartOptions.Tooltip;
        enlargedOptions.DataLabels = ChartOptions.DataLabels;

        return enlargedOptions;
    }
}