/* _content/Dashboard_Yuntech/Components/Layout/MainLayout.razor.rz.scp.css */
/* _content/Dashboard_Yuntech/Components/Layout/NavMenu.razor.rz.scp.css */
/* _content/Dashboard_Yuntech/Components/Pages/AzureCosmos.razor.rz.scp.css */
/* 風格2：優雅極簡風格 */
.elegant-tabs[b-tmwuqn7iug] {
    background: transparent;
    border: none;
    border-bottom: 1px solid #444;
    margin-bottom: 0;
}

.elegant-tab[b-tmwuqn7iug] {
    background: transparent;
    border: none;
    color: #C4E1E1;
    font-weight: 500;
    padding: 16px 32px;
    position: relative;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: bold;
}

    .elegant-tab[b-tmwuqn7iug]::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #3585D4, #66B3FF);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .elegant-tab:hover[b-tmwuqn7iug] {
        color: #3585D4;
        background: rgba(53, 133, 212, 0.05);
    }

        .elegant-tab:hover[b-tmwuqn7iug]::after {
            width: 80%;
        }

    .elegant-tab.active[b-tmwuqn7iug] {
        color: #85C2FF;
        background: rgba(53, 133, 212, 0.1);
    }

        .elegant-tab.active[b-tmwuqn7iug]::after {
            width: 100%;
            background: linear-gradient(90deg, #3585D4, #66B3FF, #3585D4);
        }
/* _content/Dashboard_Yuntech/Components/Pages/Class_CadreSarch.razor.rz.scp.css */
:root[b-bfkuoaql0y] {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #e74c3c;
    --light-gray: #f5f5f5;
    --dark-gray: #333;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body[b-bfkuoaql0y] {
    background-color: #f0f2f5 !important;
}

/* 客製化 Bootstrap 標籤樣式 */
.nav-tabs[b-bfkuoaql0y] {
    border-bottom: 1px solid #ddd;
    margin-bottom: 1.5rem;
}

    .nav-tabs .nav-link[b-bfkuoaql0y] {
        padding: 0.75rem 1.5rem;
        background-color: #f5f5f5; /* 原本是 var(--light-gray) */
        border: none;
        margin-right: 0.5rem;
        border-radius: 8px 8px 0 0; /* 原本是 var(--border-radius) */
        font-weight: bold;
        color: #333; /* 原本是 var(--dark-gray) */
    }

        .nav-tabs .nav-link:hover[b-bfkuoaql0y] {
            border-color: transparent;
        }

        .nav-tabs .nav-link.active[b-bfkuoaql0y] {
            background-color: #3498db !important; /* 原本是 var(--primary-color) */
            color: white;
            border-color: transparent;
        }

/* _content/Dashboard_Yuntech/Components/Pages/Common/Load.razor.rz.scp.css */
/* 載入動畫樣式 */
.loading-overlay[b-34cx3i69qp] {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10%;
    font-size: 20px;
    font-weight: bold;
}

.loading-spinner[b-34cx3i69qp] {
    border: 10px solid #f3f3f3;
    border-top: 10px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin-b-34cx3i69qp 1s linear infinite;
}

@keyframes spin-b-34cx3i69qp {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
/* _content/Dashboard_Yuntech/Components/Pages/Common/MyCard_dark.razor.rz.scp.css */
.card-container[b-q9zg9l38rj] {
    height:400px
}

.card[b-q9zg9l38rj] {
    background: var(--color-component-background);
    color: hsl(210, 5%, 50%);
}

.card-header[b-q9zg9l38rj] {
    color:white;
    border:0px;
}


@media (max-width: 1000px) {
    .card-containery[b-q9zg9l38rj] {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .card-container[b-q9zg9l38rj] {
        height: 280px;
    }
}

/* 圖表模態框樣式 */
.chart-modal-content[b-q9zg9l38rj] {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    min-height: 600px;
}

/* 確保模態框中的圖表能正確顯示 */
.chart-modal-content .apexcharts-canvas[b-q9zg9l38rj] {
    background: transparent !important;
}

/* 模態框按鈕樣式 */
.modal-header .btn-close[b-q9zg9l38rj] {
    filter: invert(1);
}

/* 放大按鈕懸停效果 */
a[data-bs-toggle="modal"] i.fa-expand[b-q9zg9l38rj] {
    transition: transform 0.2s ease;
}

a[data-bs-toggle="modal"]:hover i.fa-expand[b-q9zg9l38rj] {
    transform: scale(1.1);
}

/* 確保圖表在模態框中的響應式 */
.chart-modal-content > div[b-q9zg9l38rj] {
    width: 100% !important;
    height: 600px !important;
}
/* _content/Dashboard_Yuntech/Components/Pages/Common/MyCard_dark2.razor.rz.scp.css */
/*.card-container {
    height:400px
}*/

.card[b-4ozr9omo2s] {
    background: var(--color-component-background);
    color: hsl(210, 5%, 50%);
}

.card-header[b-4ozr9omo2s] {
    color:white;
    border:0px;
}


@media (max-width: 1000px) {
    .card-containery[b-4ozr9omo2s] {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .card-container[b-4ozr9omo2s] {
        height: 280px;
    }
}

/* 圖表模態框樣式 */
.chart-modal-content[b-4ozr9omo2s] {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    min-height: 600px;
}

/* 確保模態框中的圖表能正確顯示 */
.chart-modal-content .apexcharts-canvas[b-4ozr9omo2s] {
    background: transparent !important;
}

/* 模態框按鈕樣式 */
.modal-header .btn-close[b-4ozr9omo2s] {
    filter: invert(1);
}

/* 放大按鈕懸停效果 */
a[data-bs-toggle="modal"] i.fa-expand[b-4ozr9omo2s] {
    transition: transform 0.2s ease;
}

a[data-bs-toggle="modal"]:hover i.fa-expand[b-4ozr9omo2s] {
    transform: scale(1.1);
}

/* 確保圖表在模態框中的響應式 */
.chart-modal-content > div[b-4ozr9omo2s] {
    width: 100% !important;
    height: 600px !important;
}
/* _content/Dashboard_Yuntech/Components/Pages/Common/MyDataGrid.razor.rz.scp.css */
/*分頁樣式*/
[b-nas2rcta1u] .pagination {
    background-color: #F5F5F5;
    border-radius: 50rem;
    padding: .5rem 1.5rem;

}

/*分頁數字樣式*/
[b-nas2rcta1u] .page-item.active .page-link {
    background-color: #7DD1E8 !important;
    
}
    [b-nas2rcta1u] .page-item.active .page-link span {
        color: white !important;
    }

/*上下一頁 disabled時*/
[b-nas2rcta1u] .page-item.disabled .page-link i {
    color: #D0D0D0 !important;
}

/*上下一頁 可以選時*/
/*::deep .page-item[aria-disabled="false"]:not(.d-none) .page-link {
    background-color: #7DD1E8 !important;
}*/
[b-nas2rcta1u] .page-item[aria-disabled="false"] .page-link i {
    color: #7DD1E8 !important;
}

/*上下一頁間距*/
[b-nas2rcta1u] .pagination .page-item:nth-child(2) {
    margin-right: 15px !important;
}

[b-nas2rcta1u] .pagination .page-item:nth-last-child(4) {
    margin-left: 15px !important;
}

[b-nas2rcta1u] .page-item {
    z-index: 0 !important;
}
/* _content/Dashboard_Yuntech/Components/Pages/Common/TableExcel.razor.rz.scp.css */
.excel-table-wrapper[b-nlr84jmxy5] {
    width: 100%;
    overflow-x: auto;
    /* 這會使此元素忽略父元素中定義的滾動條樣式 */
    scrollbar-width: auto;
}

/* 為Excel表格添加獨立的滾動條樣式，這將覆蓋全域樣式 */
.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}

.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 4px !important;
}

.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

.excel-table[b-nlr84jmxy5] {
    min-width: 1000px; /* 設定最小寬度，確保表格不會太擠 */
    width: 100%;
}

    .excel-table th[b-nlr84jmxy5] {
        padding: 10px 15px !important;
        white-space: nowrap; /* 標題不換行 */
        min-width: 100px; /* 標題最小寬度 */
        background-color: #f5f5f5 !important;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .excel-table td[b-nlr84jmxy5] {
        padding: 8px 15px !important;
    }
/* _content/Dashboard_Yuntech/Components/Pages/MyQuickGrid.razor.rz.scp.css */

[b-01ifdea9l5] tr {
    height: 2.5em; /* 設定每列的高度，讓表格更整齊 */
}


    /* 隱藏空白的資料列 */
    [b-01ifdea9l5] tr:has(> td:not(:empty)) > td {
        display: table-cell;
    }

[b-01ifdea9l5] td:empty {
    display: none;
}


/* 使用 ::deep 來確保 CSS 能夠影響 Blazor QuickGrid 內的 Paginator */
/*::deep .paginator button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;*/ /* 設定固定寬度，避免變形 */
    /*height: 35px;
    border-radius: 5px;
    background-color: #f8f9fa;
    border: 1px solid #ccc;
    transition: background-color 0.2s;
}

    ::deep .paginator button:hover {
        background-color: #007bff;
        color: white;
    }*/

    /* ✅ 修改不同按鈕的內容，改成 FontAwesome 或 Unicode 圖標 */
    /*::deep .paginator button:first-child::before {
        content: "⏮";*/ /* 最前頁 (First) */
    /*}

    ::deep .paginator button:nth-child(2)::before {
        content: "◀";*/ /* 上一頁 (Previous) */
    /*}

    ::deep .paginator button:nth-last-child(2)::before {
        content: "▶";*/ /* 下一頁 (Next) */
    /*}

    ::deep .paginator button:last-child::before {
        content: "⏭";*/ /* 最後頁 (Last) */
    /*}*/

    /* ✅ 隱藏原本的按鈕文字 */
    [b-01ifdea9l5] .paginator button span {
        display: none;
    }

/*::deep .go-previous {
    background-color: #78c2ad;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256 246.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/></svg>');
}*/
