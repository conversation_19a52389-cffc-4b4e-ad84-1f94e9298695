/*.card-container {
    height:400px
}*/

.card {
    background: var(--color-component-background);
    color: hsl(210, 5%, 50%);
}

.card-header {
    color:white;
    border:0px;
}


@media (max-width: 1000px) {
    .card-containery {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .card-container {
        height: 280px;
    }
}

/* 圖表模態框樣式 */
.chart-modal-content {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    min-height: 600px;
}

/* 確保模態框中的圖表能正確顯示 */
.chart-modal-content .apexcharts-canvas {
    background: transparent !important;
}

/* 模態框按鈕樣式 */
.modal-header .btn-close {
    filter: invert(1);
}

/* 放大按鈕懸停效果 */
a[data-bs-toggle="modal"] i.fa-expand {
    transition: transform 0.2s ease;
}

a[data-bs-toggle="modal"]:hover i.fa-expand {
    transform: scale(1.1);
}

/* 確保圖表在模態框中的響應式 */
.chart-modal-content > div {
    width: 100% !important;
    height: 600px !important;
}