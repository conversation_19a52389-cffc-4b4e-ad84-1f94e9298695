/*.card-container {
    height:400px
}*/

.card[b-4ozr9omo2s] {
    background: var(--color-component-background);
    color: hsl(210, 5%, 50%);
}

.card-header[b-4ozr9omo2s] {
    color:white;
    border:0px;
}


@media (max-width: 1000px) {
    .card-containery[b-4ozr9omo2s] {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .card-container[b-4ozr9omo2s] {
        height: 280px;
    }
}

/* 圖表模態框樣式 */
.chart-modal-content[b-4ozr9omo2s] {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    min-height: 600px;
}

/* 確保模態框中的圖表能正確顯示 */
.chart-modal-content .apexcharts-canvas[b-4ozr9omo2s] {
    background: transparent !important;
}

/* 模態框按鈕樣式 */
.modal-header .btn-close[b-4ozr9omo2s] {
    filter: invert(1);
}

/* 放大按鈕懸停效果 */
a[data-bs-toggle="modal"] i.fa-expand[b-4ozr9omo2s] {
    transition: transform 0.2s ease;
}

a[data-bs-toggle="modal"]:hover i.fa-expand[b-4ozr9omo2s] {
    transform: scale(1.1);
}

/* 確保圖表在模態框中的響應式 */
.chart-modal-content > div[b-4ozr9omo2s] {
    width: 100% !important;
    height: 600px !important;
}